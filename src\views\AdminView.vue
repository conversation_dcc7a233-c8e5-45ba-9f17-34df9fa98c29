<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
              <Settings class="w-6 h-6 mr-3 text-blue-600" />
              Admin Panel
            </h1>
            <p class="text-gray-600 mt-1">Manage your live TV platform</p>
          </div>

          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500">
              Last updated: {{ lastUpdated }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <Tv class="w-6 h-6 text-blue-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Events</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.totalEvents }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <Users class="w-6 h-6 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Active Users</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.activeUsers }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="p-2 bg-red-100 rounded-lg">
              <Radio class="w-6 h-6 text-red-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Live Channels</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.liveChannels }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <BarChart class="w-6 h-6 text-yellow-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Views</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.totalViews.toLocaleString() }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Event Management -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Event Management</h2>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <button @click="requireAuth(() => showAddEventModal = true)"
                class="w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Plus class="w-5 h-5 mr-2" />
                Add New Event
              </button>

              <button @click="refreshEvents"
                class="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                <RefreshCw class="w-5 h-5 mr-2" />
                Refresh Events
              </button>

              <button @click="exportEvents"
                class="w-full flex items-center justify-center px-4 py-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors">
                <Download class="w-5 h-5 mr-2" />
                Export Events
              </button>

              <button @click="requireAuth(() => showCustomEventsModal = true)"
                class="w-full flex items-center justify-center px-4 py-3 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors">
                <Calendar class="w-5 h-5 mr-2" />
                Manage Custom Events
              </button>
            </div>
          </div>
        </div>

        <!-- Channel Management -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Channel Management</h2>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <button @click="showAddChannelModal = true"
                class="w-full flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                <Plus class="w-5 h-5 mr-2" />
                Add New Channel
              </button>

              <button @click="testChannels"
                class="w-full flex items-center justify-center px-4 py-3 bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-colors">
                <AlertCircle class="w-5 h-5 mr-2" />
                Test All Channels
              </button>

              <button @click="manageChannels"
                class="w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                <Edit class="w-5 h-5 mr-2" />
                Manage Channels
              </button>
            </div>
          </div>
        </div>

        <!-- System Settings -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">System Settings</h2>
          </div>

          <div class="p-6">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Auto-refresh events</span>
                <button @click="toggleAutoRefresh" :class="[
                  'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                  autoRefresh ? 'bg-blue-600' : 'bg-gray-200'
                ]">
                  <span :class="[
                    'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                    autoRefresh ? 'translate-x-6' : 'translate-x-1'
                  ]" />
                </button>
              </div>

              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Maintenance mode</span>
                <button @click="toggleMaintenanceMode" :class="[
                  'relative inline-flex h-6 w-11 items-center rounded-full transition-colors',
                  maintenanceMode ? 'bg-red-600' : 'bg-gray-200'
                ]">
                  <span :class="[
                    'inline-block h-4 w-4 transform rounded-full bg-white transition-transform',
                    maintenanceMode ? 'translate-x-6' : 'translate-x-1'
                  ]" />
                </button>
              </div>

              <button @click="clearCache"
                class="w-full flex items-center justify-center px-4 py-3 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors">
                <Trash class="w-5 h-5 mr-2" />
                Clear Cache
              </button>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow-sm">
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Recent Activity</h2>
          </div>

          <div class="p-6">
            <div class="space-y-3">
              <div v-for="activity in recentActivity" :key="activity.id" class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div class="flex-1">
                  <p class="text-sm text-gray-900">{{ activity.message }}</p>
                  <p class="text-xs text-gray-500">{{ activity.time }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Custom Event Modal -->
    <div v-if="showAddEventModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto modal">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Add Custom Match</h3>
            <button @click="closeAddEventModal" class="text-gray-400 hover:text-gray-600">
              <X class="w-6 h-6" />
            </button>
          </div>
          <p class="text-sm text-gray-600 mt-1">Create a custom match with direct iframe link</p>
        </div>

        <form @submit.prevent="submitCustomEvent" class="p-6 space-y-6">
          <!-- Match Details -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Match Name *</label>
              <input v-model="customEventForm.match" type="text" required placeholder="e.g., Real Madrid vs Barcelona"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900"
                style="background-color: white !important; color: #1f2937 !important;" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Sport *</label>
              <select v-model="customEventForm.sport" required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">Select Sport</option>
                <option value="Football">⚽ Football</option>
                <option value="Basketball">🏀 Basketball</option>
                <option value="Baseball">⚾ Baseball</option>
                <option value="Tennis">🎾 Tennis</option>
                <option value="Hockey">🏒 Hockey</option>
                <option value="Boxing">🥊 Boxing</option>
                <option value="MMA">🥊 MMA</option>
                <option value="Cricket">🏏 Cricket</option>
                <option value="Rugby">🏉 Rugby</option>
                <option value="Golf">⛳ Golf</option>
                <option value="Racing">🏎️ Racing</option>
                <option value="Other">🏆 Other</option>
              </select>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Tournament/League *</label>
            <input v-model="customEventForm.tournament" type="text" required
              placeholder="e.g., UEFA Champions League, NBA Finals"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
          </div>

          <!-- Date and Time -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Date *</label>
              <input v-model="customEventForm.date" type="date" required :min="today"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Time *</label>
              <input v-model="customEventForm.time" type="time" required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            </div>
          </div>

          <!-- Iframe URL -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Iframe URL *</label>
            <input v-model="customEventForm.iframeUrl" type="url" required
              placeholder="https://example.com/embed/stream"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
            <p class="text-xs text-gray-500 mt-1">Direct link to the streaming iframe</p>
          </div>

          <!-- Description -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Description (Optional)</label>
            <textarea v-model="customEventForm.description" rows="3" placeholder="Additional details about the match..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
          </div>

          <!-- Preview -->
          <div v-if="customEventForm.match && customEventForm.sport" class="bg-gray-50 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-700 mb-2">Preview:</h4>
            <div class="bg-white rounded-lg p-4 border">
              <div class="flex items-center space-x-2 mb-2">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ getSportIcon(customEventForm.sport) }} {{ customEventForm.sport }}
                </span>
                <span
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <div class="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                  CUSTOM
                </span>
              </div>
              <h3 class="font-semibold text-gray-900">{{ customEventForm.match }}</h3>
              <p class="text-sm text-gray-600">{{ customEventForm.tournament }}</p>
              <p class="text-xs text-gray-500 mt-1">
                {{ formatPreviewDateTime(customEventForm.date, customEventForm.time) }}
              </p>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
            <button type="button" @click="closeAddEventModal"
              class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
              Cancel
            </button>
            <button type="submit" :disabled="!isFormValid"
              class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
              Add Custom Match
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Custom Events List Modal -->
    <div v-if="showCustomEventsModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Custom Events</h3>
            <button @click="showCustomEventsModal = false" class="text-gray-400 hover:text-gray-600">
              <X class="w-6 h-6" />
            </button>
          </div>
        </div>

        <div class="p-6">
          <div v-if="Object.keys(store.customEvents).length === 0" class="text-center py-8">
            <Calendar class="w-16 h-16 mx-auto text-gray-300 mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">No custom events</h3>
            <p class="text-gray-600">Create your first custom match to get started.</p>
          </div>

          <div v-else class="space-y-4">
            <div v-for="(dayEvents, date) in store.customEvents" :key="date" class="bg-gray-50 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-3">{{ formatDate(date) }}</h4>
              <div class="space-y-3">
                <div v-for="event in dayEvents" :key="event.id"
                  class="bg-white rounded-lg p-4 flex items-center justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {{ getSportIcon(event.sport) }} {{ event.sport }}
                      </span>
                      <span
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <div class="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                        CUSTOM
                      </span>
                    </div>
                    <h3 class="font-semibold text-gray-900">{{ event.match }}</h3>
                    <p class="text-sm text-gray-600">{{ event.tournament }}</p>
                    <p class="text-xs text-gray-500">{{ apiService.getEventDateTime(event.unix_timestamp) }}</p>
                  </div>
                  <button @click="removeCustomEvent(event.id!)"
                    class="ml-4 p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                    <Trash class="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Admin Login Modal -->
    <div v-if="showLoginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="p-6">
          <div class="text-center mb-6">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
              <Settings class="h-6 w-6 text-blue-600" />
            </div>
            <h3 class="text-lg font-semibold text-gray-900">Admin Access Required</h3>
            <p class="text-sm text-gray-600 mt-2">Enter admin password to continue</p>
          </div>

          <form @submit.prevent="adminLogin" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
              <input v-model="adminPassword" type="password" required placeholder="Enter admin password"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900"
                style="background-color: white !important; color: #1f2937 !important;" />
            </div>

            <div class="flex items-center justify-end space-x-4 pt-4">
              <button type="button" @click="showLoginModal = false; adminPassword = ''"
                class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                Cancel
              </button>
              <button type="submit"
                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Login
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useLiveTvStore } from '@/stores/counter'
import { apiService } from '@/services/api'
import {
  Settings,
  Tv,
  Users,
  Radio,
  BarChart,
  Plus,
  RefreshCw,
  Download,
  AlertCircle,
  Edit,
  Trash,
  X,
  Calendar
} from 'lucide-vue-next'

const store = useLiveTvStore()

const showAddEventModal = ref(false)
const showAddChannelModal = ref(false)
const showCustomEventsModal = ref(false)
const showLoginModal = ref(false)
const autoRefresh = ref(true)
const maintenanceMode = ref(false)
const adminPassword = ref('')

// Check if user is authenticated
const isAuthenticated = computed(() => {
  return localStorage.getItem('admin_authenticated') === 'true'
})

// Custom event form
const customEventForm = ref({
  match: '',
  sport: '',
  tournament: '',
  date: '',
  time: '',
  iframeUrl: '',
  description: ''
})

const today = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const stats = ref({
  totalEvents: 0,
  activeUsers: 1247,
  liveChannels: 0,
  totalViews: 45678
})

const recentActivity = ref([
  { id: 1, message: 'New event added: Lakers vs Celtics', time: '2 minutes ago' },
  { id: 2, message: 'Channel ESPN updated', time: '5 minutes ago' },
  { id: 3, message: 'System cache cleared', time: '10 minutes ago' },
  { id: 4, message: 'User admin logged in', time: '15 minutes ago' }
])

const lastUpdated = computed(() => {
  return new Date().toLocaleTimeString()
})

const isFormValid = computed(() => {
  const form = customEventForm.value
  const isValid = !!(
    form.match?.trim() &&
    form.sport?.trim() &&
    form.tournament?.trim() &&
    form.date?.trim() &&
    form.time?.trim() &&
    form.iframeUrl?.trim()
  )

  console.log('Form validation:', {
    match: form.match,
    sport: form.sport,
    tournament: form.tournament,
    date: form.date,
    time: form.time,
    iframeUrl: form.iframeUrl,
    isValid
  })

  return isValid
})

const refreshEvents = async () => {
  await store.fetchEvents()
  updateStats()
}

const exportEvents = () => {
  // Implement export functionality
  alert('Events exported successfully!')
}

const testChannels = () => {
  // Implement channel testing
  alert('Testing all channels...')
}

const manageChannels = () => {
  // Navigate to channel management
  alert('Channel management coming soon!')
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
}

const toggleMaintenanceMode = () => {
  maintenanceMode.value = !maintenanceMode.value
}

const clearCache = () => {
  // Implement cache clearing
  alert('Cache cleared successfully!')
}

const updateStats = () => {
  stats.value.totalEvents = Object.values(store.allEvents).flat().length
  stats.value.liveChannels = store.channels.filter(c => c.isLive).length
}

// Custom event methods
const closeAddEventModal = () => {
  showAddEventModal.value = false
  resetCustomEventForm()
}

const resetCustomEventForm = () => {
  customEventForm.value = {
    match: '',
    sport: '',
    tournament: '',
    date: '',
    time: '',
    iframeUrl: '',
    description: ''
  }
}

const submitCustomEvent = async () => {
  console.log('Submit button clicked')
  console.log('Form valid:', isFormValid.value)
  console.log('Form data:', customEventForm.value)

  if (!isFormValid.value) {
    alert('Please fill in all required fields')
    return
  }

  try {
    console.log('Adding custom event...')
    const newEvent = store.addCustomEvent(customEventForm.value)
    console.log('Event added:', newEvent)

    // Add to recent activity
    recentActivity.value.unshift({
      id: Date.now(),
      message: `Custom event added: ${newEvent.match}`,
      time: 'Just now'
    })

    // Update stats
    updateStats()

    // Close modal and reset form
    closeAddEventModal()

    alert('Custom match added successfully!')
  } catch (error) {
    console.error('Error adding custom event:', error)
    alert(`Error adding custom match: ${error.message || error}`)
  }
}

const removeCustomEvent = (eventId: string) => {
  if (confirm('Are you sure you want to remove this custom event?')) {
    store.removeCustomEvent(eventId)
    updateStats()

    // Add to recent activity
    recentActivity.value.unshift({
      id: Date.now(),
      message: 'Custom event removed',
      time: 'Just now'
    })
  }
}

const getSportIcon = (sport: string): string => {
  return apiService.getSportIcon(sport)
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  if (date.toDateString() === today.toDateString()) {
    return 'Today'
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return 'Tomorrow'
  } else {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
}

const formatPreviewDateTime = (date: string, time: string): string => {
  if (!date || !time) return ''

  const dateTime = new Date(`${date} ${time}`)
  return dateTime.toLocaleString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  })
}

// Admin authentication
const adminLogin = () => {
  const correctPassword = 'admin123' // Change this to your preferred password
  if (adminPassword.value === correctPassword) {
    localStorage.setItem('admin_authenticated', 'true')
    showLoginModal.value = false
    adminPassword.value = ''
  } else {
    alert('Incorrect password')
    adminPassword.value = ''
  }
}

const adminLogout = () => {
  localStorage.removeItem('admin_authenticated')
}

const requireAuth = (action: () => void) => {
  if (isAuthenticated.value) {
    action()
  } else {
    showLoginModal.value = true
  }
}

onMounted(async () => {
  await store.fetchEvents()
  await store.fetchChannels()
  store.loadCustomEvents()
  updateStats()
})
</script>
