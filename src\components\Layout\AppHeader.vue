<template>
  <header class="bg-white shadow-lg sticky top-0 z-50">
    <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-2">
            <div
              class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Tv class="w-5 h-5 text-white" />
            </div>
            <span class="text-xl font-bold text-gray-900">LiveTV</span>
          </router-link>
        </div>

        <!-- Navigation Links -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <router-link to="/" class="nav-link" :class="{ 'nav-link-active': $route.name === 'home' }">
              <Home class="w-4 h-4 mr-2" />
              Home
            </router-link>
            <router-link to="/live" class="nav-link" :class="{ 'nav-link-active': $route.name === 'live' }">
              <Radio class="w-4 h-4 mr-2" />
              Live TV
            </router-link>
            <router-link to="/schedule" class="nav-link" :class="{ 'nav-link-active': $route.name === 'schedule' }">
              <Calendar class="w-4 h-4 mr-2" />
              Schedule
            </router-link>
            <!-- Admin link only visible when authenticated or in development -->
            <router-link v-if="showAdminLink" to="/admin/login" class="nav-link"
              :class="{ 'nav-link-active': $route.name === 'admin' || $route.name === 'admin-login' }">
              <Settings class="w-4 h-4 mr-2" />
              Admin
            </router-link>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button @click="toggleMobileMenu"
            class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500">
            <Menu v-if="!mobileMenuOpen" class="w-6 h-6" />
            <X v-else class="w-6 h-6" />
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div v-show="mobileMenuOpen" class="md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 rounded-lg mt-2">
          <router-link to="/" class="mobile-nav-link" @click="closeMobileMenu">
            <Home class="w-4 h-4 mr-2" />
            Home
          </router-link>
          <router-link to="/live" class="mobile-nav-link" @click="closeMobileMenu">
            <Radio class="w-4 h-4 mr-2" />
            Live TV
          </router-link>
          <router-link to="/schedule" class="mobile-nav-link" @click="closeMobileMenu">
            <Calendar class="w-4 h-4 mr-2" />
            Schedule
          </router-link>
          <!-- Admin link only visible when authenticated or in development -->
          <router-link v-if="showAdminLink" to="/admin/login" class="mobile-nav-link" @click="closeMobileMenu">
            <Settings class="w-4 h-4 mr-2" />
            Admin
          </router-link>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Tv, Home, Radio, Calendar, Settings, Menu, X } from 'lucide-vue-next'

const mobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

// Show admin link only in development or when authenticated
const showAdminLink = computed(() => {
  // Show in development mode
  if (import.meta.env.DEV) return true

  // Show if user is already authenticated
  return localStorage.getItem('admin_authenticated') === 'true'
})
</script>

<style scoped>
.nav-link {
  @apply flex items-center px-3 py-2 rounded-md text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200;
}

.nav-link-active {
  @apply text-blue-600 bg-blue-50;
}

.mobile-nav-link {
  @apply flex items-center px-3 py-2 rounded-md text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-200 transition-colors duration-200;
}
</style>
