<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Console Test - LiveTV</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { border-left: 4px solid #10b981; }
        .warning { border-left: 4px solid #f59e0b; }
        .error { border-left: 4px solid #ef4444; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2563eb; }
        code {
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🔒 Complete Security Test</h1>
    
    <div class="test-box warning">
        <h3>⚠️ Security Test Instructions</h3>
        <p><strong>Try these actions - ALL should be BLOCKED:</strong></p>
        <ul>
            <li>Press <code>F12</code> - Should be blocked</li>
            <li>Right-click anywhere - Should be blocked</li>
            <li>Press <code>Ctrl+Shift+I</code> - Should be blocked</li>
            <li>Press <code>Ctrl+U</code> (View Source) - Should be blocked</li>
            <li>Try to select text - Should be blocked</li>
        </ul>
    </div>

    <div class="test-box">
        <h3>🧪 Console Tests</h3>
        <button onclick="testConsoleLog()">Test console.log</button>
        <button onclick="testConsoleError()">Test console.error</button>
        <button onclick="testConsoleWarn()">Test console.warn</button>
        <button onclick="testConsoleInfo()">Test console.info</button>
        <button onclick="testAPIResponse()">Test API Response Log</button>
        <button onclick="testSensitiveData()">Test Sensitive Data Log</button>
    </div>

    <div class="test-box success">
        <h3>✅ Expected Security Results</h3>
        <p>If security is working correctly:</p>
        <ul>
            <li>❌ F12 key blocked - Developer tools won't open</li>
            <li>❌ Right-click blocked - Context menu won't appear</li>
            <li>❌ Keyboard shortcuts blocked - Ctrl+Shift+I, Ctrl+U, etc.</li>
            <li>❌ Text selection blocked - Can't select/copy text</li>
            <li>❌ Console access blocked - No way to see API responses</li>
            <li>❌ Network tab blocked - Can't inspect network requests</li>
            <li>🔒 Warning messages appear when trying blocked actions</li>
        </ul>
    </div>

    <div class="test-box error">
        <h3>❌ If You See Console Output</h3>
        <p>Console is NOT disabled. Users can see:</p>
        <ul>
            <li>API responses and data</li>
            <li>Error messages</li>
            <li>Debug information</li>
            <li>Sensitive application data</li>
        </ul>
        <p>Check <code>src/main.ts</code> and ensure <code>DISABLE_CONSOLE = true</code></p>
    </div>

    <div class="test-box">
        <h3>🔧 Development Mode</h3>
        <p>To enable console during development:</p>
        <ol>
            <li>Open <code>src/main.ts</code></li>
            <li>Change <code>const DISABLE_CONSOLE = true</code> to <code>false</code></li>
            <li>Restart the development server</li>
        </ol>
    </div>

    <script>
        function testConsoleLog() {
            console.log('🔍 TEST: This is a console.log message');
            console.log('API Response:', { events: { '2024-12-21': [{ match: 'Test vs Test' }] } });
        }

        function testConsoleError() {
            console.error('❌ TEST: This is a console.error message');
            console.error('API Error:', new Error('Failed to fetch'));
        }

        function testConsoleWarn() {
            console.warn('⚠️ TEST: This is a console.warn message');
        }

        function testConsoleInfo() {
            console.info('ℹ️ TEST: This is a console.info message');
        }

        function testAPIResponse() {
            console.log('🌐 Simulated API Response:', {
                success: true,
                events: {
                    '2024-12-21': [
                        {
                            match: 'Real Madrid vs Barcelona',
                            sport: 'Football',
                            channels: ['https://topembed.pw/embed/channel1'],
                            unix_timestamp: 1703174400
                        }
                    ]
                }
            });
        }

        function testSensitiveData() {
            console.log('🔐 Sensitive Data Test:', {
                apiKey: 'secret-api-key-12345',
                adminPassword: 'admin123',
                databaseUrl: 'https://secret-database.com',
                userTokens: ['token1', 'token2', 'token3']
            });
        }

        // Test on page load
        window.addEventListener('load', () => {
            console.log('🚀 Page loaded - Console test ready');
            console.log('If you can see this message, console is NOT disabled');
        });
    </script>
</body>
</html>
