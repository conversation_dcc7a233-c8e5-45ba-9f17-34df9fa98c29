import axios from 'axios'

export interface LiveEvent {
  unix_timestamp: number
  sport: string
  tournament: string
  match: string
  channels: string[]
}

export interface LiveEventsResponse {
  events: {
    [date: string]: LiveEvent[]
  }
}

export interface Channel {
  id: string
  name: string
  url: string
  category: string
  isLive: boolean
}

class ApiService {
  private baseURL = 'https://topembed.pw/api.php'

  constructor() {
    axios.defaults.timeout = 15000
    // Configure axios for CORS
    axios.defaults.headers.common['Accept'] = 'application/json'
  }

  // Fetch live events from your actual API
  async getLiveEvents(date?: string): Promise<LiveEventsResponse> {
    try {
      console.log('Fetching events from API:', this.baseURL)

      const response = await axios.get<LiveEventsResponse>(this.baseURL, {
        // Add any query parameters if needed
        params: date ? { date } : {},
        // Handle CORS if needed
        headers: {
          'Content-Type': 'application/json',
        }
      })

      console.log('API Response:', response.data)
      return response.data
    } catch (error) {
      console.error('Error fetching events from API:', error)

      // Fallback to mock data if API fails
      console.log('Using fallback mock data')
      return this.getMockData()
    }
  }

  // Fallback mock data based on your API structure
  private getMockData(): LiveEventsResponse {
    return {
      events: {
        '2025-06-20': [
          {
            unix_timestamp: 1750372800,
            sport: 'Baseball',
            tournament: 'MLB',
            match: 'Miami Marlins - Philadelphia Phillies',
            channels: [
              'https://topembed.pw/channel/FanDuelSportsFlorida[USA]',
              'https://topembed.pw/channel/NBCSPhiladelphia[USA]'
            ]
          },
          {
            unix_timestamp: 1750373100,
            sport: 'Football',
            tournament: 'CONCACAF Gold Cup',
            match: 'Trinidad and Tobago - Haiti',
            channels: [
              'https://topembed.pw/channel/FOXSports1[USA]'
            ]
          },
          {
            unix_timestamp: 1750374900,
            sport: 'Baseball',
            tournament: 'MLB',
            match: 'Atlanta Braves - New York Mets',
            channels: [
              'https://topembed.pw/channel/FanDuelSportsSoutheast[USA]',
              'https://topembed.pw/channel/MLBNetwork[USA]'
            ]
          },
          {
            unix_timestamp: 1750435200,
            sport: 'Football',
            tournament: 'FIFA Club World Cup 2025',
            match: 'Benfica - Auckland City',
            channels: [
              'https://topembed.pw/channel/DirecTVSports2[Argentina]',
              'https://topembed.pw/channel/ProArena[Romania]',
              'https://topembed.pw/channel/SuperSportLaLiga[SouthAfrica]',
              'https://topembed.pw/channel/SuperSportPremierLeague[SouthAfrica]',
              'https://topembed.pw/channel/ex7137796'
            ]
          }
        ],
        '2025-07-19': [
          {
            unix_timestamp: 1752912000,
            sport: 'Sailing',
            tournament: 'SailGP',
            match: 'SailGP Portsmouth - Day 1',
            channels: [
              'https://topembed.pw/channel/SkySportsAction[UK]'
            ]
          }
        ],
        '2025-07-20': [
          {
            unix_timestamp: 1752998400,
            sport: 'Sailing',
            tournament: 'SailGP',
            match: 'SailGP Portsmouth - Day 2',
            channels: [
              'https://topembed.pw/channel/SkySportsAction[UK]'
            ]
          }
        ]
      }
    }
  }

  async getChannels(): Promise<Channel[]> {
    // Mock channels data
    const mockChannels: Channel[] = [
      {
        id: '1',
        name: 'ESPN',
        url: 'https://topembed.pw/channel/ESPN[USA]',
        category: 'Sports',
        isLive: true
      },
      {
        id: '2',
        name: 'FOX Sports',
        url: 'https://topembed.pw/channel/FOXSports[USA]',
        category: 'Sports',
        isLive: true
      },
      {
        id: '3',
        name: 'NBC Sports',
        url: 'https://topembed.pw/channel/NBCSports[USA]',
        category: 'Sports',
        isLive: true
      }
    ]

    await new Promise(resolve => setTimeout(resolve, 500))
    return mockChannels
  }

  getChannelName(url: string): string {
    // Extract channel name from URL
    const match = url.match(/\/channel\/([^[]+)/)
    if (match) {
      let name = match[1]
      // Handle special cases like ex7137796
      if (name.startsWith('ex')) {
        return `Channel ${name.substring(2)}`
      }
      // Add spaces before capital letters for better readability
      return name.replace(/([A-Z])/g, ' $1').trim()
    }
    return 'Unknown Channel'
  }

  getChannelRegion(url: string): string {
    // Extract region from URL like [USA], [UK], etc.
    const match = url.match(/\[([^\]]+)\]/)
    return match ? match[1] : 'Global'
  }

  getEventTime(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  getEventDate(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  getEventDateTime(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  isEventLive(timestamp: number): boolean {
    const now = Date.now() / 1000
    const eventTime = timestamp
    // Consider event live if it started within the last 3 hours
    return now >= eventTime && now <= eventTime + (3 * 60 * 60)
  }

  getTimeUntilEvent(timestamp: number): string {
    const now = Date.now() / 1000
    const eventTime = timestamp
    const diff = eventTime - now

    if (diff < 0) {
      if (this.isEventLive(timestamp)) {
        return 'LIVE NOW'
      } else {
        return 'Ended'
      }
    }

    const days = Math.floor(diff / (24 * 60 * 60))
    const hours = Math.floor((diff % (24 * 60 * 60)) / (60 * 60))
    const minutes = Math.floor((diff % (60 * 60)) / 60)

    if (days > 0) {
      return `in ${days}d ${hours}h`
    } else if (hours > 0) {
      return `in ${hours}h ${minutes}m`
    } else if (minutes > 0) {
      return `in ${minutes}m`
    } else {
      return 'Starting soon'
    }
  }

  getSportIcon(sport: string): string {
    const sportIcons: { [key: string]: string } = {
      'Baseball': '⚾',
      'Football': '⚽',
      'Basketball': '🏀',
      'Soccer': '⚽',
      'Tennis': '🎾',
      'Hockey': '🏒',
      'Golf': '⛳',
      'Boxing': '🥊',
      'MMA': '🥊',
      'Racing': '🏎️',
      'Sailing': '⛵',
      'Cricket': '🏏',
      'Rugby': '🏉',
      'Volleyball': '🏐'
    }
    return sportIcons[sport] || '🏆'
  }

  getAllSports(events: { [date: string]: LiveEvent[] }): string[] {
    const sports = new Set<string>()
    Object.values(events).flat().forEach(event => {
      sports.add(event.sport)
    })
    return Array.from(sports).sort()
  }

  getEventsByDate(events: { [date: string]: LiveEvent[] }, targetDate: string): LiveEvent[] {
    return events[targetDate] || []
  }

  filterLiveEvents(events: { [date: string]: LiveEvent[] }): LiveEvent[] {
    const liveEvents: LiveEvent[] = []
    Object.values(events).flat().forEach(event => {
      if (this.isEventLive(event.unix_timestamp)) {
        liveEvents.push(event)
      }
    })
    return liveEvents.sort((a, b) => a.unix_timestamp - b.unix_timestamp)
  }

  getUpcomingEvents(events: { [date: string]: LiveEvent[] }, limit: number = 10): LiveEvent[] {
    const now = Date.now() / 1000
    const upcomingEvents: LiveEvent[] = []

    Object.values(events).flat().forEach(event => {
      if (event.unix_timestamp > now) {
        upcomingEvents.push(event)
      }
    })

    return upcomingEvents
      .sort((a, b) => a.unix_timestamp - b.unix_timestamp)
      .slice(0, limit)
  }
}

export const apiService = new ApiService()
