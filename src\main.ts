import './assets/main.css'

// Import security modules to protect from users
import './utils/console-disabler'
import { consoleDisabler } from './utils/console-disabler'
import './utils/devtools-blocker'
import { devToolsBlocker } from './utils/devtools-blocker'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// Security settings - Set to false during development
const DISABLE_CONSOLE = true
const BLOCK_DEVTOOLS = true

// Disable console to hide API responses
if (DISABLE_CONSOLE) {
  consoleDisabler.forceDisable()
  console.log('🔒 Console disabled - Users cannot see API responses')
}

// Block developer tools completely
if (BLOCK_DEVTOOLS) {
  devToolsBlocker.enable()
  console.log('🚫 Developer tools blocked - F12, right-click, inspect disabled')
}

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
