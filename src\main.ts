import './assets/main.css'

// Import console disabler to hide logs from users
import './utils/console-disabler'
import { consoleDisabler } from './utils/console-disabler'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// Force disable console to hide API responses from users
// Comment this line during development if you need to see logs
consoleDisabler.forceDisable()

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
