<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
              <Calendar class="w-6 h-6 mr-3 text-blue-600" />
              Event Schedule
            </h1>
            <p class="text-gray-600 mt-1">Upcoming sports events and matches</p>
          </div>

          <div class="flex items-center space-x-4">
            <select v-model="selectedSport"
              class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="">All Sports</option>
              <option v-for="sport in availableSports" :key="sport" :value="sport">
                {{ apiService.getSportIcon(sport) }} {{ sport }}
              </option>
            </select>

            <button @click="refreshEvents" :disabled="loading"
              class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors">
              <RefreshCw :class="['w-4 h-4 mr-2', { 'animate-spin': loading }]" />
              Refresh
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="loading" class="space-y-4">
        <div v-for="i in 5" :key="i" class="bg-white rounded-lg shadow-sm p-6">
          <div class="animate-pulse">
            <div class="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div class="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>

      <!-- Events List -->
      <div v-else class="space-y-6">
        <div v-for="(dayEvents, date) in filteredEvents" :key="date"
          class="bg-white rounded-lg shadow-sm overflow-hidden">
          <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">
              {{ formatDate(date) }}
            </h2>
            <p class="text-sm text-gray-600">{{ dayEvents.length }} event(s)</p>
          </div>

          <div class="divide-y divide-gray-200">
            <div v-for="event in dayEvents" :key="event.unix_timestamp"
              class="p-6 hover:bg-gray-50 transition-colors cursor-pointer" @click="goToLive(event)">
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="flex items-center space-x-3 mb-2">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {{ apiService.getSportIcon(event.sport) }} {{ event.sport }}
                    </span>
                    <span v-if="apiService.isEventLive(event.unix_timestamp)"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      <div class="w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"></div>
                      LIVE
                    </span>
                    <span class="text-sm text-gray-500">{{ event.tournament }}</span>
                    <span class="text-sm font-medium text-gray-900">
                      {{ apiService.getEventTime(event.unix_timestamp) }}
                    </span>
                  </div>

                  <h3 class="text-lg font-semibold text-gray-900 mb-1">
                    {{ event.match }}
                  </h3>

                  <div class="flex items-center space-x-4 text-sm text-gray-600">
                    <div class="flex items-center">
                      <Tv class="w-4 h-4 mr-1" />
                      {{ event.channels.length }} channel(s)
                    </div>
                    <div class="flex items-center">
                      <Clock class="w-4 h-4 mr-1" />
                      {{ apiService.getTimeUntilEvent(event.unix_timestamp) }}
                    </div>
                  </div>
                </div>

                <div class="flex items-center space-x-3">
                  <button @click.stop="setReminder(event)"
                    class="inline-flex items-center px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                    <Bell class="w-4 h-4 mr-1" />
                    Remind
                  </button>

                  <button @click.stop="goToLive(event)"
                    class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    <Play class="w-4 h-4 mr-1" />
                    Watch
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="Object.keys(filteredEvents).length === 0" class="text-center py-12">
          <Calendar class="w-16 h-16 mx-auto text-gray-300 mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">No events found</h3>
          <p class="text-gray-600">
            {{ selectedSport ? `No ${selectedSport.toLowerCase()} events scheduled` : 'No events scheduled for the
            selected period' }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useLiveTvStore } from '@/stores/counter'
import { apiService } from '@/services/api'
import {
  Calendar,
  Tv,
  Clock,
  Bell,
  Play,
  RefreshCw
} from 'lucide-vue-next'

const router = useRouter()
const store = useLiveTvStore()
const selectedSport = ref('')

const loading = computed(() => store.loading)
const events = computed(() => store.events)

const filteredEvents = computed(() => {
  if (!selectedSport.value) return store.eventsByDate

  return store.getEventsBySport(selectedSport.value)
})

const availableSports = computed(() => {
  return store.allSports
})

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)

  if (date.toDateString() === today.toDateString()) {
    return 'Today'
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return 'Tomorrow'
  } else {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
}

const getTimeUntilEvent = (timestamp: number) => {
  const now = Date.now()
  const eventTime = timestamp * 1000
  const diff = eventTime - now

  if (diff < 0) return 'Live now'

  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (hours > 24) {
    const days = Math.floor(hours / 24)
    return `in ${days} day${days > 1 ? 's' : ''}`
  } else if (hours > 0) {
    return `in ${hours}h ${minutes}m`
  } else {
    return `in ${minutes}m`
  }
}

const refreshEvents = async () => {
  await store.fetchEvents()
}

const setReminder = (event: any) => {
  // Implement reminder functionality
  alert(`Reminder set for ${event.match}`)
}

const goToLive = (event: any) => {
  store.selectEvent(event)
  router.push('/live')
}

onMounted(async () => {
  await store.fetchEvents()
})
</script>
