// Secure authentication service
import Crypto<PERSON><PERSON> from 'crypto-js'

class AuthService {
  private readonly SECRET_KEY = 'LiveTV_Admin_2024_Secure_Key_#$%'
  private readonly SESSION_DURATION = 24 * 60 * 60 * 1000 // 24 hours
  private readonly ADMIN_HASH = 'e3afed0047b08059d0fada10f400c1e5' // MD5 of 'admin123' for compatibility

  // Hash password using MD5 for compatibility
  private hashPassword(password: string): string {
    return CryptoJS.MD5(password).toString()
  }

  // Encrypt data
  private encrypt(data: string): string {
    return CryptoJS.AES.encrypt(data, this.SECRET_KEY).toString()
  }

  // Decrypt data
  private decrypt(encryptedData: string): string {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, this.SECRET_KEY)
      return bytes.toString(CryptoJS.enc.Utf8)
    } catch {
      return ''
    }
  }

  // Generate secure session token
  private generateSessionToken(): string {
    const timestamp = Date.now()
    const randomData = Math.random().toString(36).substring(2, 15)
    const sessionData = `${timestamp}:${randomData}`
    return this.encrypt(sessionData)
  }

  // Validate session token
  private validateSessionToken(token: string): boolean {
    try {
      const decrypted = this.decrypt(token)
      if (!decrypted) return false

      const [timestamp] = decrypted.split(':')
      const tokenTime = parseInt(timestamp)
      const now = Date.now()

      // Check if token is within valid time range
      return (now - tokenTime) < this.SESSION_DURATION
    } catch {
      return false
    }
  }

  // Login with password
  async login(password: string): Promise<{ success: boolean; message: string }> {
    try {
      // Add artificial delay to prevent brute force
      await new Promise(resolve => setTimeout(resolve, 1000))

      const hashedPassword = this.hashPassword(password)
      
      if (hashedPassword === this.ADMIN_HASH) {
        const sessionToken = this.generateSessionToken()
        const loginTime = Date.now().toString()

        // Store encrypted session data
        localStorage.setItem('admin_session', sessionToken)
        localStorage.setItem('admin_login_time', this.encrypt(loginTime))
        localStorage.setItem('admin_authenticated', 'true')

        return {
          success: true,
          message: 'Login successful'
        }
      } else {
        return {
          success: false,
          message: 'Invalid credentials'
        }
      }
    } catch (error) {
      return {
        success: false,
        message: 'Login failed'
      }
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    try {
      const sessionToken = localStorage.getItem('admin_session')
      const authFlag = localStorage.getItem('admin_authenticated')

      if (!sessionToken || authFlag !== 'true') {
        return false
      }

      // Validate session token
      if (!this.validateSessionToken(sessionToken)) {
        this.logout()
        return false
      }

      return true
    } catch {
      this.logout()
      return false
    }
  }

  // Logout and clear all session data
  logout(): void {
    localStorage.removeItem('admin_session')
    localStorage.removeItem('admin_login_time')
    localStorage.removeItem('admin_authenticated')
  }

  // Get session info
  getSessionInfo(): { loginTime: number | null; timeRemaining: number } {
    try {
      const encryptedLoginTime = localStorage.getItem('admin_login_time')
      if (!encryptedLoginTime) return { loginTime: null, timeRemaining: 0 }

      const loginTimeStr = this.decrypt(encryptedLoginTime)
      const loginTime = parseInt(loginTimeStr)
      const now = Date.now()
      const timeRemaining = Math.max(0, this.SESSION_DURATION - (now - loginTime))

      return { loginTime, timeRemaining }
    } catch {
      return { loginTime: null, timeRemaining: 0 }
    }
  }

  // Check if admin link should be visible
  shouldShowAdminLink(): boolean {
    // Show in development mode
    if (import.meta.env.DEV) return true
    
    // Show if user is authenticated
    return this.isAuthenticated()
  }

  // Auto-logout when session expires
  startSessionMonitoring(): void {
    setInterval(() => {
      if (!this.isAuthenticated()) {
        // Session expired, redirect if on admin page
        if (window.location.pathname.startsWith('/admin') && 
            window.location.pathname !== '/admin/login') {
          window.location.href = '/admin/login'
        }
      }
    }, 60000) // Check every minute
  }
}

export const authService = new AuthService()
