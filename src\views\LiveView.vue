<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
              <div class="live-indicator mr-3"></div>
              Live TV
            </h1>
            <p class="text-gray-600 mt-1">Watch live sports events now</p>
          </div>

          <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <div class="flex items-center space-x-4 text-sm text-gray-600">
              <span>{{ filteredEvents.length }} {{ searchQuery || selectedSport ? 'filtered' : '' }} events</span>
              <span class="text-red-600 font-medium">
                {{ searchQuery || selectedSport ? filteredLiveEvents.length : liveEvents.length }} live now
              </span>
              <span>{{ allAvailableEvents.length }} total events</span>
            </div>

            <div class="flex items-center space-x-3">
              <!-- Search Input -->
              <div class="relative">
                <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500" />
                <input v-model="searchQuery" type="text" placeholder="Search events..."
                  class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64 bg-white text-gray-900 placeholder-gray-500 font-medium"
                  style="background-color: white !important; color: #1f2937 !important; font-weight: 500;" />
              </div>

              <!-- Sport Filter -->
              <select v-model="selectedSport"
                class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 font-medium"
                style="background-color: white !important; color: #1f2937 !important;">
                <option value=""
                  style="background-color: white !important; color: #1f2937 !important; font-weight: 600;">All Sports
                </option>
                <option v-for="sport in allSports" :key="sport" :value="sport"
                  style="background-color: white !important; color: #1f2937 !important; font-weight: 600;">
                  {{ apiService.getSportIcon(sport) }} {{ sport }}
                </option>
              </select>

              <button @click="refreshEvents" :disabled="loading"
                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors">
                <RefreshCw :class="['w-4 h-4 mr-2', { 'animate-spin': loading }]" />
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-8">
        <!-- Events List -->
        <div class="lg:col-span-1 order-2 lg:order-1">
          <div class="bg-white rounded-lg shadow-sm">
            <div class="p-4 lg:p-6 border-b border-gray-200">
              <h2 class="text-base lg:text-lg font-semibold text-gray-900">
                {{ searchQuery || selectedSport ? 'Filtered Events' : "All Events" }}
              </h2>
            </div>

            <div class="max-h-96 overflow-y-auto">
              <div v-if="loading" class="p-6">
                <div class="animate-pulse space-y-4">
                  <div v-for="i in 3" :key="i" class="h-20 bg-gray-200 rounded"></div>
                </div>
              </div>

              <div v-else-if="filteredEvents.length === 0" class="p-6 text-center text-gray-500">
                <Tv class="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No events found</p>
                <p class="text-sm mt-2">Try adjusting your search or filter</p>
              </div>

              <div v-else class="p-4 space-y-6">
                <!-- Custom Events Section (Featured) -->
                <div v-if="customEventsForDisplay.length > 0">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-bold text-green-700 flex items-center">
                      <Crown class="w-4 h-4 mr-2 text-green-600" />
                      Admin Featured Events
                    </h3>
                    <span class="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full font-medium">
                      {{ customEventsForDisplay.length }} custom
                    </span>
                  </div>

                  <div class="space-y-3">
                    <div v-for="event in customEventsForDisplay" :key="event.id"
                      class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border-2 border-green-200 hover:border-green-300 hover:shadow-lg transition-all cursor-pointer relative group"
                      @click="selectEvent(event)">

                      <!-- Admin Delete Button -->
                      <button v-if="isAdmin" @click.stop="deleteCustomEvent(event.id!)"
                        class="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-lg z-10">
                        <Trash class="w-3 h-3" />
                      </button>

                      <div class="flex items-start justify-between pr-8">
                        <div class="flex-1">
                          <div class="flex items-center space-x-2 mb-2">
                            <span
                              class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-green-500 text-white shadow-sm">
                              <Crown class="w-3 h-3 mr-1" />
                              ADMIN SPECIAL
                            </span>
                            <span
                              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white text-green-700 border border-green-200">
                              {{ apiService.getSportIcon(event.sport) }} {{ event.sport }}
                            </span>
                            <span class="text-xs text-green-600 font-medium">{{
                              apiService.getEventTime(event.unix_timestamp) }}</span>
                          </div>

                          <h3 class="font-bold text-gray-900 text-base mb-1">{{ event.match }}</h3>
                          <p class="text-sm text-gray-700 font-medium">{{ event.tournament }}</p>

                          <div class="flex items-center space-x-4 mt-2">
                            <div class="flex items-center text-xs text-green-700">
                              <Tv class="w-3 h-3 mr-1" />
                              Direct Stream
                            </div>
                            <div v-if="event.description" class="flex items-center text-xs text-gray-600">
                              <Info class="w-3 h-3 mr-1" />
                              {{ event.description }}
                            </div>
                          </div>
                        </div>

                        <div class="flex flex-col items-center">
                          <div class="bg-green-500 rounded-full p-2 mb-1">
                            <Play class="w-4 h-4 text-white" />
                          </div>
                          <span class="text-xs text-green-600 font-medium">Watch Now</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Regular Events List -->
                <div v-if="regularEventsForDisplay.length > 0">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-semibold text-gray-700">{{ customEventsForDisplay.length > 0 ? 'More Events'
                      : 'All Events' }}</h3>
                    <span class="text-xs text-gray-500">{{ regularEventsForDisplay.length }} events</span>
                  </div>

                  <div class="divide-y divide-gray-200">
                    <div v-for="event in regularEventsForDisplay" :key="event.unix_timestamp"
                      @click="selectEvent(event)" :class="[
                        'p-4 cursor-pointer hover:bg-gray-50 transition-colors',
                        { 'bg-blue-50 border-r-4 border-blue-500': selectedEvent?.unix_timestamp === event.unix_timestamp }
                      ]">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <div class="flex items-center space-x-2 mb-1">
                            <!-- Live Event Indicator -->
                            <span v-if="apiService.isEventLive(event.unix_timestamp)"
                              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              <div class="w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"></div>
                              LIVE
                            </span>
                            <!-- Upcoming Event Indicator -->
                            <span v-else
                              class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <div class="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
                              UPCOMING
                            </span>
                            <span class="text-xs text-gray-500">{{ apiService.getEventTime(event.unix_timestamp)
                            }}</span>
                          </div>
                          <h3 class="font-medium text-gray-900 text-sm">{{ event.match }}</h3>
                          <p class="text-xs text-gray-600 mt-1">{{ event.tournament }} • {{ event.sport }}</p>
                          <p class="text-xs text-blue-600 mt-1">{{ event.channels.length }} channel(s)</p>
                        </div>
                        <Play class="w-4 h-4 text-gray-400" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Video Player -->
        <div class="lg:col-span-2 order-1 lg:order-2">
          <div class="bg-white rounded-lg shadow-sm">
            <div v-if="!selectedEvent" class="aspect-video bg-gray-900 rounded-t-lg flex items-center justify-center">
              <div class="text-center text-white px-4">
                <Tv class="w-12 lg:w-16 h-12 lg:h-16 mx-auto mb-4 text-gray-300" />
                <h3 class="text-lg lg:text-xl font-semibold mb-2 text-white">Select an Event</h3>
                <p class="text-sm lg:text-base text-gray-300">Choose a live event from the list to start watching</p>
              </div>
            </div>

            <div v-else>
              <!-- Video Player -->
              <div class="aspect-video bg-black rounded-t-lg relative">
                <!-- Custom Event with Full Iframe HTML -->
                <div v-if="selectedChannel && selectedEvent?.isCustom" v-html="selectedChannel"
                  class="w-full h-full rounded-t-lg iframe-container">
                </div>

                <!-- Regular Event with URL -->
                <iframe v-else-if="selectedChannel" :src="selectedChannel"
                  allow="autoplay; fullscreen; encrypted-media; accelerometer; gyroscope; picture-in-picture"
                  allowfullscreen width="100%" height="100%" scrolling="no" frameborder="0"
                  referrerpolicy="no-referrer-when-downgrade"
                  sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-presentation"
                  class="w-full h-full rounded-t-lg" style="border: none; outline: none;">
                </iframe>

                <div v-else class="w-full h-full flex items-center justify-center text-white">
                  <div class="text-center">
                    <AlertCircle class="w-12 h-12 mx-auto mb-4 text-yellow-400" />
                    <p>No channel selected</p>
                  </div>
                </div>
              </div>

              <!-- Player Controls -->
              <div class="p-4 lg:p-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4 space-y-2 lg:space-y-0">
                  <div class="flex-1">
                    <h2 class="text-lg lg:text-xl font-bold text-gray-900">{{ selectedEvent.match }}</h2>
                    <p class="text-sm lg:text-base text-gray-600">{{ selectedEvent.tournament }} • {{
                      selectedEvent.sport }}</p>
                    <p v-if="selectedEvent.description" class="text-xs lg:text-sm text-gray-500 mt-1">{{
                      selectedEvent.description }}</p>
                  </div>

                  <div class="flex items-center space-x-2 lg:ml-4">
                    <span v-if="selectedEvent.isCustom"
                      class="custom-indicator text-xs lg:text-sm font-medium">CUSTOM</span>
                    <span v-else-if="apiService.isEventLive(selectedEvent.unix_timestamp)"
                      class="live-indicator text-xs lg:text-sm font-medium">LIVE</span>
                    <span v-else class="upcoming-indicator text-xs lg:text-sm font-medium">UPCOMING</span>
                  </div>
                </div>

                <!-- Channel Selection -->
                <div v-if="selectedEvent.channels.length > 1">
                  <h3 class="text-sm font-medium text-gray-900 mb-3">Available Channels:</h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    <button v-for="(channel, index) in selectedEvent.channels" :key="index"
                      @click="selectChannel(channel)" :class="[
                        'p-3 text-sm rounded-lg border transition-colors text-left',
                        selectedChannel === channel
                          ? 'bg-blue-600 text-white border-blue-600'
                          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                      ]">
                      <div class="font-medium">{{ apiService.getChannelName(channel) }}</div>
                      <div class="text-xs opacity-75 mt-1">{{ apiService.getChannelRegion(channel) }}</div>
                    </button>
                  </div>
                </div>

                <!-- Single Channel Info -->
                <div v-else-if="selectedEvent.channels.length === 1" class="bg-gray-50 rounded-lg p-3">
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <div class="font-medium text-gray-900">{{ apiService.getChannelName(selectedEvent.channels[0]) }}
                      </div>
                      <div class="text-sm text-gray-600">{{ apiService.getChannelRegion(selectedEvent.channels[0]) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import { useLiveTvStore } from '@/stores/counter'
import { apiService } from '@/services/api'
import {
  Tv,
  Play,
  RefreshCw,
  AlertCircle,
  Search,
  Trash,
  Crown,
  Info
} from 'lucide-vue-next'

const store = useLiveTvStore()
const searchQuery = ref('')
const selectedSport = ref('')

const loading = computed(() => store.loading)
const todayEvents = computed(() => store.todayEvents)
const liveEvents = computed(() => store.liveEvents)
const totalEvents = computed(() => store.totalEvents)
const allSports = computed(() => store.allSports)
const selectedEvent = computed(() => store.selectedEvent)
const selectedChannel = computed(() => store.selectedChannel)

// Get all events from all dates (including custom events)
const allAvailableEvents = computed(() => {
  const allEvents = []

  // Get all events from all dates
  Object.values(store.allEvents).forEach(dayEvents => {
    allEvents.push(...dayEvents)
  })

  return allEvents
})

const filteredEvents = computed(() => {
  let events = allAvailableEvents.value

  // Filter by sport
  if (selectedSport.value) {
    events = events.filter(event => event.sport === selectedSport.value)
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    events = events.filter(event =>
      event.match.toLowerCase().includes(query) ||
      event.sport.toLowerCase().includes(query) ||
      event.tournament.toLowerCase().includes(query) ||
      (event.description && event.description.toLowerCase().includes(query))
    )
  }

  // Sort by timestamp (most recent first)
  return events.sort((a, b) => b.unix_timestamp - a.unix_timestamp)
})

// Count live events in filtered results
const filteredLiveEvents = computed(() => {
  return filteredEvents.value.filter(event =>
    event.isCustom || apiService.isEventLive(event.unix_timestamp)
  )
})

// Separate custom and regular events for special display
const customEventsForDisplay = computed(() => {
  return filteredEvents.value.filter(event => event.isCustom)
})

const regularEventsForDisplay = computed(() => {
  return filteredEvents.value.filter(event => !event.isCustom)
})

// Check if user is admin
const isAdmin = computed(() => {
  return localStorage.getItem('admin_authenticated') === 'true'
})

const selectEvent = (event: any) => {
  store.selectEvent(event)
}

const selectChannel = (channel: string) => {
  store.selectChannel(channel)
}

const refreshEvents = async () => {
  await store.fetchEvents()
}

const deleteCustomEvent = async (eventId: string) => {
  if (confirm('Are you sure you want to delete this custom event?')) {
    try {
      await store.removeCustomEvent(eventId)
      // If this was the selected event, clear selection
      if (selectedEvent.value && (selectedEvent.value as any).id === eventId) {
        store.clearSelection()
      }
    } catch (error) {
      alert('Error deleting event. Please try again.')
    }
  }
}

onMounted(async () => {
  await store.fetchEvents()
  await store.fetchChannels()
  await store.loadCustomEvents()
  store.subscribeToCustomEvents()
})

onUnmounted(() => {
  store.unsubscribeFromCustomEvents()
})
</script>

<style scoped>
.live-indicator {
  color: #dc2626;
  font-weight: 600;
}

.live-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

.custom-indicator {
  color: #059669;
  font-weight: 600;
}

.custom-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  margin-right: 8px;
}

.upcoming-indicator {
  color: #2563eb;
  font-weight: 600;
}

.upcoming-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  margin-right: 8px;
}

/* Iframe container for custom events */
.iframe-container {
  position: relative;
  overflow: hidden;
}

.iframe-container :deep(iframe) {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 0.5rem 0.5rem 0 0 !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

/* Responsive improvements */
@media (max-width: 1024px) {
  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .lg\:col-span-2 {
    grid-column: span 1 / span 1;
  }
}

@media (max-width: 768px) {
  .aspect-video {
    aspect-ratio: 16 / 9;
    min-height: 200px;
  }

  .max-h-96 {
    max-height: 50vh;
  }

  .p-6 {
    padding: 1rem;
  }

  .space-y-6> :not([hidden])~ :not([hidden]) {
    margin-top: 1rem;
  }
}

@media (max-width: 640px) {
  .grid-cols-1.sm\:grid-cols-2.lg\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .text-xl {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .p-4 {
    padding: 0.75rem;
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}
</style>
