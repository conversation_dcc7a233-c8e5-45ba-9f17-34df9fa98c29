<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
              <div class="live-indicator mr-3"></div>
              Live TV
            </h1>
            <p class="text-gray-600 mt-1">Watch live sports events now</p>
          </div>

          <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <div class="flex items-center space-x-4 text-sm text-gray-600">
              <span>{{ filteredEvents.length }} events</span>
              <span class="text-red-600 font-medium">{{ liveEvents.length }} live now</span>
              <span>{{ totalEvents }} total events</span>
            </div>

            <div class="flex items-center space-x-3">
              <!-- Search Input -->
              <div class="relative">
                <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input v-model="searchQuery" type="text" placeholder="Search events..."
                  class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64" />
              </div>

              <!-- Sport Filter -->
              <select v-model="selectedSport"
                class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">All Sports</option>
                <option v-for="sport in allSports" :key="sport" :value="sport">
                  {{ apiService.getSportIcon(sport) }} {{ sport }}
                </option>
              </select>

              <button @click="refreshEvents" :disabled="loading"
                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors">
                <RefreshCw :class="['w-4 h-4 mr-2', { 'animate-spin': loading }]" />
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Events List -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200">
              <h2 class="text-lg font-semibold text-gray-900">
                {{ searchQuery || selectedSport ? 'Filtered Events' : "Today's Events" }}
              </h2>
            </div>

            <div class="max-h-96 overflow-y-auto">
              <div v-if="loading" class="p-6">
                <div class="animate-pulse space-y-4">
                  <div v-for="i in 3" :key="i" class="h-20 bg-gray-200 rounded"></div>
                </div>
              </div>

              <div v-else-if="filteredEvents.length === 0" class="p-6 text-center text-gray-500">
                <Tv class="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No events found</p>
                <p class="text-sm mt-2">Try adjusting your search or filter</p>
              </div>

              <div v-else class="divide-y divide-gray-200">
                <div v-for="event in filteredEvents" :key="event.unix_timestamp" @click="selectEvent(event)" :class="[
                  'p-4 cursor-pointer hover:bg-gray-50 transition-colors',
                  { 'bg-blue-50 border-r-4 border-blue-500': selectedEvent?.unix_timestamp === event.unix_timestamp }
                ]">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center space-x-2 mb-1">
                        <span
                          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          <div class="w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"></div>
                          LIVE
                        </span>
                        <span class="text-xs text-gray-500">{{ apiService.getEventTime(event.unix_timestamp) }}</span>
                      </div>
                      <h3 class="font-medium text-gray-900 text-sm">{{ event.match }}</h3>
                      <p class="text-xs text-gray-600 mt-1">{{ event.tournament }} • {{ event.sport }}</p>
                      <p class="text-xs text-blue-600 mt-1">{{ event.channels.length }} channel(s)</p>
                    </div>
                    <Play class="w-4 h-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Video Player -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-lg shadow-sm">
            <div v-if="!selectedEvent" class="aspect-video bg-gray-900 rounded-t-lg flex items-center justify-center">
              <div class="text-center text-white">
                <Tv class="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <h3 class="text-xl font-semibold mb-2 text-white">Select an Event</h3>
                <p class="text-gray-300">Choose a live event from the list to start watching</p>
              </div>
            </div>

            <div v-else>
              <!-- Video Player -->
              <div class="aspect-video bg-black rounded-t-lg relative">
                <iframe v-if="selectedChannel" :src="selectedChannel" allow="encrypted-media" width="100%" height="100%"
                  scrolling="no" frameborder="0" allowfullscreen class="w-full h-full rounded-t-lg"></iframe>

                <div v-else class="w-full h-full flex items-center justify-center text-white">
                  <div class="text-center">
                    <AlertCircle class="w-12 h-12 mx-auto mb-4 text-yellow-400" />
                    <p>No channel selected</p>
                  </div>
                </div>
              </div>

              <!-- Player Controls -->
              <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                  <div>
                    <h2 class="text-xl font-bold text-gray-900">{{ selectedEvent.match }}</h2>
                    <p class="text-gray-600">{{ selectedEvent.tournament }} • {{ selectedEvent.sport }}</p>
                  </div>

                  <div class="flex items-center space-x-2">
                    <span class="live-indicator text-sm font-medium">LIVE</span>
                  </div>
                </div>

                <!-- Channel Selection -->
                <div v-if="selectedEvent.channels.length > 1">
                  <h3 class="text-sm font-medium text-gray-900 mb-3">Available Channels:</h3>
                  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    <button v-for="(channel, index) in selectedEvent.channels" :key="index"
                      @click="selectChannel(channel)" :class="[
                        'p-3 text-sm rounded-lg border transition-colors text-left',
                        selectedChannel === channel
                          ? 'bg-blue-600 text-white border-blue-600'
                          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                      ]">
                      <div class="font-medium">{{ apiService.getChannelName(channel) }}</div>
                      <div class="text-xs opacity-75 mt-1">{{ apiService.getChannelRegion(channel) }}</div>
                    </button>
                  </div>
                </div>

                <!-- Single Channel Info -->
                <div v-else-if="selectedEvent.channels.length === 1" class="bg-gray-50 rounded-lg p-3">
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <div class="font-medium text-gray-900">{{ apiService.getChannelName(selectedEvent.channels[0]) }}
                      </div>
                      <div class="text-sm text-gray-600">{{ apiService.getChannelRegion(selectedEvent.channels[0]) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useLiveTvStore } from '@/stores/counter'
import { apiService } from '@/services/api'
import {
  Tv,
  Play,
  RefreshCw,
  AlertCircle,
  Search
} from 'lucide-vue-next'

const store = useLiveTvStore()
const searchQuery = ref('')
const selectedSport = ref('')

const loading = computed(() => store.loading)
const todayEvents = computed(() => store.todayEvents)
const liveEvents = computed(() => store.liveEvents)
const totalEvents = computed(() => store.totalEvents)
const allSports = computed(() => store.allSports)
const selectedEvent = computed(() => store.selectedEvent)
const selectedChannel = computed(() => store.selectedChannel)

const filteredEvents = computed(() => {
  let events = todayEvents.value

  // Filter by sport
  if (selectedSport.value) {
    events = events.filter(event => event.sport === selectedSport.value)
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    events = events.filter(event =>
      event.match.toLowerCase().includes(query) ||
      event.sport.toLowerCase().includes(query) ||
      event.tournament.toLowerCase().includes(query)
    )
  }

  return events
})

const selectEvent = (event: any) => {
  store.selectEvent(event)
}

const selectChannel = (channel: string) => {
  store.selectChannel(channel)
}

const refreshEvents = async () => {
  await store.fetchEvents()
}

onMounted(async () => {
  await store.fetchEvents()
  await store.fetchChannels()
})
</script>

<style scoped>
.live-indicator {
  color: #dc2626;
  font-weight: 600;
}

.live-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}
</style>
